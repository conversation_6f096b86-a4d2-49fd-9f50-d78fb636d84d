// Performance Monitor - Optional debugging tool
// Uncomment the script tag in HTML to enable performance monitoring

class PerformanceMonitor {
    constructor() {
        this.isEnabled = false; // Set to true to enable monitoring
        this.frameCount = 0;
        this.lastTime = performance.now();
        this.fps = 0;
        this.fpsHistory = [];
        this.maxHistory = 60; // Keep last 60 FPS readings
        
        if (this.isEnabled) {
            this.init();
        }
    }

    init() {
        // Create performance display
        this.createDisplay();
        
        // Start monitoring
        this.startMonitoring();
        
        // Monitor memory usage if available
        if (performance.memory) {
            this.monitorMemory();
        }
    }

    createDisplay() {
        const display = document.createElement('div');
        display.id = 'performance-monitor';
        display.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            min-width: 200px;
        `;
        document.body.appendChild(display);
        this.display = display;
    }

    startMonitoring() {
        const monitor = () => {
            const currentTime = performance.now();
            const deltaTime = currentTime - this.lastTime;
            
            this.frameCount++;
            
            // Calculate FPS every second
            if (deltaTime >= 1000) {
                this.fps = Math.round((this.frameCount * 1000) / deltaTime);
                this.fpsHistory.push(this.fps);
                
                if (this.fpsHistory.length > this.maxHistory) {
                    this.fpsHistory.shift();
                }
                
                this.frameCount = 0;
                this.lastTime = currentTime;
                
                this.updateDisplay();
            }
            
            requestAnimationFrame(monitor);
        };
        
        requestAnimationFrame(monitor);
    }

    monitorMemory() {
        setInterval(() => {
            if (performance.memory && this.display) {
                const memory = performance.memory;
                const usedMB = Math.round(memory.usedJSHeapSize / 1048576);
                const totalMB = Math.round(memory.totalJSHeapSize / 1048576);
                const limitMB = Math.round(memory.jsHeapSizeLimit / 1048576);
                
                this.memoryInfo = `Memory: ${usedMB}/${totalMB}MB (Limit: ${limitMB}MB)`;
                this.updateDisplay();
            }
        }, 2000);
    }

    updateDisplay() {
        if (!this.display) return;
        
        const avgFPS = this.fpsHistory.length > 0 ? 
            Math.round(this.fpsHistory.reduce((a, b) => a + b, 0) / this.fpsHistory.length) : 0;
        
        const minFPS = this.fpsHistory.length > 0 ? Math.min(...this.fpsHistory) : 0;
        const maxFPS = this.fpsHistory.length > 0 ? Math.max(...this.fpsHistory) : 0;
        
        let html = `
            <div><strong>Performance Monitor</strong></div>
            <div>Current FPS: ${this.fps}</div>
            <div>Average FPS: ${avgFPS}</div>
            <div>Min/Max FPS: ${minFPS}/${maxFPS}</div>
        `;
        
        if (this.memoryInfo) {
            html += `<div>${this.memoryInfo}</div>`;
        }
        
        // Performance warnings
        if (this.fps < 30) {
            html += `<div style="color: #ff6b6b;">⚠️ Low FPS detected</div>`;
        } else if (this.fps < 45) {
            html += `<div style="color: #ffd93d;">⚠️ Moderate FPS</div>`;
        } else {
            html += `<div style="color: #6bcf7f;">✅ Good FPS</div>`;
        }
        
        this.display.innerHTML = html;
    }

    // Method to enable/disable monitoring
    toggle() {
        this.isEnabled = !this.isEnabled;
        if (this.isEnabled && !this.display) {
            this.init();
        } else if (!this.isEnabled && this.display) {
            this.display.remove();
            this.display = null;
        }
    }
}

// Initialize performance monitor (disabled by default)
const performanceMonitor = new PerformanceMonitor();

// Expose to global scope for debugging
window.performanceMonitor = performanceMonitor;

// Console helper
console.log('Performance Monitor available. Use performanceMonitor.toggle() to enable/disable.');
