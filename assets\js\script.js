
// Text Line-by-line Animation
(function() {
  let textAnim;
  const textSplitters = [];

  const doTextLines = () => {
    textAnim && textAnim.progress(1);
    
    // Clear previous instances
    textSplitters.forEach(splitter => splitter.revert());
    textSplitters.length = 0;

    // Process each text element individually
    document.querySelectorAll("[data-split='lines']").forEach(element => {
      // Split text for animation
      const typeSplit = new SplitText(element, { 
        types: "lines", 
        linesClass: "lineChild"
      });
      
      // Create mask for smooth effect
      const maskSplit = new SplitText(element, { 
        types: "lines", 
        linesClass: "lineParent"
      });
      
      textSplitters.push(typeSplit, maskSplit);

      // Animation setup for this element
      gsap.set(typeSplit.lines, { opacity: 1 });
      
      textAnim = gsap.fromTo(typeSplit.lines, { yPercent: 200 }, {
        yPercent: 0,
        opacity: 1,
        duration: 1,
        stagger: 0.08,
        delay: -0.2,
        ease: "power1.out",
        scrollTrigger: {
          trigger: element,
          start: "top 90%",
          end: "bottom 20%",
          markers: false
        }
      });
    });
  };

  // DOM Ready Event
  document.addEventListener("DOMContentLoaded", () => {
    doTextLines();  // Lines animation with mask
  });

  // Resize event - Optimized debouncing
  let resizeTimeout;
  window.addEventListener("resize", () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      doTextLines();  // Lines animation refresh
    }, 250); // Increased debounce time for better performance
  });
})();


// HOME GALLERY SCROLL EFFECT - Performance Optimized
(function() {
  // Cache DOM elements for better performance
  const galleryWrapper = document.querySelector(".gallery-wrapper");
  const sideCols = document.querySelectorAll(".col:not(.main)");
  const mainImg = document.querySelector(".img.main img");
  const screenWidth = window.innerWidth;
  const maxScale = screenWidth < 900 ? 4 : 3.5;

  ScrollTrigger.create({
    trigger: ".sticky-image-gallery-end",
    start: "top bottom",
    end: "bottom bottom",
    scrub: 1,
    onUpdate: (self) => {
      // Use transform3d for hardware acceleration
      const scale = 1 + self.progress * maxScale;
      const yTranslate = self.progress * 300;
      const mainImgScale = 2 - self.progress * 0.85;

      // Use transform3d for better performance
      if (galleryWrapper) {
        galleryWrapper.style.transform = `translate3d(-50%, -50%, 0) scale(${scale})`;
      }

      sideCols.forEach((col) => {
        col.style.transform = `translate3d(0, ${yTranslate}px, 0)`;
      });

      if (mainImg) {
        mainImg.style.transform = `scale3d(${mainImgScale}, ${mainImgScale}, 1)`;
      }
    },
  });
})();

// FOR BUTTON REVEAL ANIMATION
(function() {
  document.querySelectorAll('.btn-cta').forEach((button) => {
    // Target elements within each button
    const border = button.querySelector('.btn-cta-border');
    const title = button.querySelector('.btn-cta-title');
    
    // Initial setup for elements
    [border, title].forEach(element => {
      gsap.set(element, {
        y: 100,
        scale: element === border ? 0.1 : 1
      });
    });

    // Animation timeline for coordinated effects
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: button,
        start: "top bottom",
        markers: false
      }
    });

    // Sequence animations
    tl.to(border, {
      y: 0,
      scale: 1,
      duration: 0.9,
      ease: "expo.out"
    }).to(title, {
      y: 0,
      duration: 0.7,
      ease: "circ.inOut"
    }, "<0.2");
  });
})();