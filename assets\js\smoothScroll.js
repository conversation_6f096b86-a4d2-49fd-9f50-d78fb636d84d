
// Initialize Lenis smooth scroll
const lenis = new Lenis({
  duration: 1, // You cam change this value to make the scroll slower or faster
  easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)), 
  orientation: 'vertical', // Scroll orientation
  gestureOrientation: 'vertical',
  smoothWheel: true,
  wheelMultiplier: 1,
  smoothTouch: false, // Disable smooth scrolling on touch devices
  touchMultiplier: 2,
  infinite: false,
})

// Get scroll value - optimized for performance
lenis.on('scroll', ({ scroll, limit, velocity, direction, progress }) => {
  // Console logging removed for better performance
  // Uncomment below line only for debugging:
  // console.log({ scroll, limit, velocity, direction, progress })
})

// RAF animation
function raf(time) {
  lenis.raf(time)
  requestAnimationFrame(raf)
}

// Start the animation
requestAnimationFrame(raf)
