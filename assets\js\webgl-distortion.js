class WebGLDistortion {
    constructor() {
        this.projectCards = [];
        this.isRendering = false;
        this.frameId = null;
        this.lastFrameTime = 0;
        this.targetFPS = 60;
        this.frameInterval = 1000 / this.targetFPS;
        this.isLowPerformanceDevice = this.detectLowPerformanceDevice();
        this.init();
    }

    // Detect low performance devices for adaptive quality
    detectLowPerformanceDevice() {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        if (!gl) return true;

        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
        if (debugInfo) {
            const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
            // Check for integrated graphics or mobile GPUs
            return /intel|mobile|arm|mali|adreno|powervr/i.test(renderer);
        }

        // Fallback: check hardware concurrency and memory
        return navigator.hardwareConcurrency < 4 || navigator.deviceMemory < 4;
    }

    // Easing functions for smooth transitions
    easeOutCubic(t) {
        return 1 - Math.pow(1 - t, 3);
    }

    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    init() {
        document.addEventListener('DOMContentLoaded', () => {
            console.log('WebGL Distortion: DOM loaded, initializing...');
            this.setupProjectCards();
        });
    }

    setupProjectCards() {
        const cards = document.querySelectorAll('.project-card');

        cards.forEach((card, index) => {
            const img = card.querySelector('img');
            if (!img) return;

            // Wait for image to load before creating WebGL
            if (img.complete) {
                this.createWebGLForCard(card, img);
            } else {
                img.addEventListener('load', () => {
                    this.createWebGLForCard(card, img);
                });
            }
        });

        // Render loop will start when needed (on hover)
    }

    createWebGLForCard(card, img) {
        // Create canvas element
        const canvas = document.createElement('canvas');
        canvas.className = 'webgl-canvas';

        // Create WebGL context
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        if (!gl) {
            console.warn('WebGL not supported, falling back to regular image');
            return;
        }

        // Store original image source
        const imageSrc = img.src;

        // Replace image with canvas
        img.style.display = 'none';
        card.insertBefore(canvas, img);

        // Initialize WebGL for this card
        const cardData = {
            canvas,
            gl,
            imageSrc,
            card,
            isHovered: false,
            program: null,
            texture: null,
            textureLoaded: false,
            needsInitialRender: true, // Flag to ensure initial render
            hasRenderedOnce: false, // Track if card has been rendered at least once
            uniforms: {},
            startTime: Date.now(),
            distortionTarget: 0,
            distortionCurrent: 0,
            transitionSpeed: 0.08, // Smooth transition speed
            transitionStartTime: 0,
            transitionDuration: this.isLowPerformanceDevice ? 400 : 600, // Faster transitions on low-end devices
            isTransitioning: false,
            transitionStartValue: 0
        };

        this.projectCards.push(cardData);
        this.initWebGL(cardData);
        this.setupEventListeners(cardData);
    }

    initWebGL(cardData) {
        const { gl, canvas, imageSrc } = cardData;

        // Set canvas size with adaptive performance optimization
        const resizeCanvas = () => {
            const rect = cardData.card.getBoundingClientRect();
            // Adaptive pixel ratio based on device performance
            const devicePixelRatio = Math.min(window.devicePixelRatio || 1, this.isLowPerformanceDevice ? 1.5 : 2);
            // Adaptive performance scale based on device capabilities
            const performanceScale = this.isLowPerformanceDevice ? 0.6 : 0.8;

            canvas.width = rect.width * devicePixelRatio * performanceScale;
            canvas.height = rect.height * devicePixelRatio * performanceScale;
            canvas.style.width = rect.width + 'px';
            canvas.style.height = rect.height + 'px';

            gl.viewport(0, 0, canvas.width, canvas.height);
        };

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // Vertex shader source
        const vertexShaderSource = `
            attribute vec2 a_position;
            attribute vec2 a_texCoord;
            varying vec2 v_texCoord;
            
            void main() {
                gl_Position = vec4(a_position, 0.0, 1.0);
                v_texCoord = a_texCoord;
            }
        `;

        // Fragment shader source with distortion effect
        const fragmentShaderSource = `
            precision mediump float;

            uniform sampler2D u_texture;
            uniform float u_time;
            uniform float u_distortion;
            uniform vec2 u_resolution;
            uniform vec2 u_imageSize;

            varying vec2 v_texCoord;

            // Enhanced noise functions
            float random(vec2 st) {
                return fract(sin(dot(st.xy, vec2(12.9898, 78.233))) * 43758.5453123);
            }

            float noise(vec2 st) {
                vec2 i = floor(st);
                vec2 f = fract(st);

                float a = random(i);
                float b = random(i + vec2(1.0, 0.0));
                float c = random(i + vec2(0.0, 1.0));
                float d = random(i + vec2(1.0, 1.0));

                vec2 u = f * f * (3.0 - 2.0 * f);

                return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
            }

            // Fractal Brownian Motion for more complex noise
            float fbm(vec2 st, int octaves) {
                float value = 0.0;
                float amplitude = 0.5;
                float frequency = 1.0;

                for (int i = 0; i < 6; i++) {
                    if (i >= octaves) break;
                    value += amplitude * noise(st * frequency);
                    amplitude *= 0.5;
                    frequency *= 2.0;
                }
                return value;
            }

            // Curl noise for fluid-like motion
            vec2 curlNoise(vec2 p, float time) {
                float eps = 0.01;
                float n1 = fbm(p + vec2(eps, 0.0) + time * 0.1, 4);
                float n2 = fbm(p + vec2(-eps, 0.0) + time * 0.1, 4);
                float n3 = fbm(p + vec2(0.0, eps) + time * 0.1, 4);
                float n4 = fbm(p + vec2(0.0, -eps) + time * 0.1, 4);

                return vec2((n3 - n4) / (2.0 * eps), (n2 - n1) / (2.0 * eps));
            }

            vec2 coverUV(vec2 uv, vec2 canvasSize, vec2 textureSize) {
                vec2 canvasAspect = canvasSize / canvasSize.y;
                vec2 textureAspect = textureSize / textureSize.y;

                vec2 scale = vec2(1.0);
                vec2 offset = vec2(0.0);

                if (canvasAspect.x > textureAspect.x) {
                    scale.y = canvasAspect.x / textureAspect.x;
                    offset.y = (1.0 - scale.y) * 0.5;
                } else {
                    scale.x = textureAspect.x / canvasAspect.x;
                    offset.x = (1.0 - scale.x) * 0.5;
                }

                return (uv - offset) / scale;
            }

            void main() {
                vec2 uv = v_texCoord;

                // Apply object-fit: cover behavior to maintain aspect ratio
                vec2 properUV = coverUV(uv, u_resolution, u_imageSize);

                // Performance-optimized distortion
                float distortionAmount = u_distortion * 0.08;

                // Center point for radial effects
                vec2 center = vec2(0.5, 0.5);
                vec2 fromCenter = properUV - center;
                float distanceFromCenter = length(fromCenter);

                // Reduced complexity turbulence for better performance
                float turbulence1 = fbm(properUV * 2.5 + u_time * 0.6, 2); // Reduced octaves and frequency
                float turbulence2 = fbm(properUV * 4.0 - u_time * 0.4, 2); // Reduced octaves and frequency

                // Simplified curl noise with reduced frequency
                vec2 curl = curlNoise(properUV * 2.0, u_time);

                // Combined turbulence
                float turbulence = mix(turbulence1, turbulence2, 0.5);

                // Radial distortion that gets stronger towards edges
                float radialStrength = smoothstep(0.0, 0.7, distanceFromCenter);

                // Performance-optimized wave patterns
                float wave1 = sin(properUV.y * 4.0 + u_time * 2.0 + turbulence * 2.0); // Reduced frequency
                float wave2 = cos(properUV.x * 3.5 + u_time * 1.5 + turbulence * 1.8); // Reduced frequency

                // Optimized spiral distortion
                float angle = atan(fromCenter.y, fromCenter.x);
                float spiral = sin(angle * 1.5 + distanceFromCenter * 8.0 + u_time * 1.2); // Reduced complexity

                // Optimized distortion combination
                vec2 distortion = vec2(
                    (turbulence - 0.5) * distortionAmount * 1.2 +
                    wave1 * distortionAmount * 0.5 +
                    spiral * distortionAmount * 0.25 * radialStrength +
                    curl.x * distortionAmount * 0.6,

                    (turbulence - 0.5) * distortionAmount * 1.0 +
                    wave2 * distortionAmount * 0.6 +
                    spiral * distortionAmount * 0.2 * radialStrength +
                    curl.y * distortionAmount * 0.6
                );

                // Simplified flowing effect
                float flow = sin(properUV.y * 4.0 + u_time * 2.0) * cos(properUV.x * 3.0 + u_time * 1.5);
                distortion += vec2(flow, flow * 0.8) * distortionAmount * 0.3;

                // Simplified pulsing effect
                float pulse = sin(u_time * 3.0) * 0.3 + 0.7;
                distortion *= pulse;

                // Sample texture with distortion
                vec2 distortedUV = properUV + distortion;

                // Wrap UV coordinates for seamless effect
                distortedUV = fract(distortedUV);

                vec4 color = texture2D(u_texture, distortedUV);

                // Enhanced chromatic aberration with multiple channels
                if (u_distortion > 0.1) {
                    vec2 aberrationR = vec2(distortionAmount * 0.012, distortionAmount * 0.008);
                    vec2 aberrationG = vec2(distortionAmount * 0.006, -distortionAmount * 0.004);
                    vec2 aberrationB = vec2(-distortionAmount * 0.010, distortionAmount * 0.006);

                    vec4 colorR = texture2D(u_texture, fract(distortedUV + aberrationR));
                    vec4 colorG = texture2D(u_texture, fract(distortedUV + aberrationG));
                    vec4 colorB = texture2D(u_texture, fract(distortedUV + aberrationB));

                    color.r = colorR.r;
                    color.g = colorG.g;
                    color.b = colorB.b;

                    // Add slight color enhancement
                    color.rgb = mix(color.rgb, color.rgb * 1.1, u_distortion * 0.3);
                }

                // Add subtle glow effect at edges during distortion
                if (u_distortion > 0.5) {
                    float edgeGlow = smoothstep(0.3, 0.8, distanceFromCenter) * u_distortion * 0.1;
                    color.rgb += vec3(edgeGlow * 0.2, edgeGlow * 0.1, edgeGlow * 0.3);
                }

                gl_FragColor = color;
            }
        `;

        // Create and compile shaders
        const vertexShader = this.createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
        const fragmentShader = this.createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);

        // Create program
        cardData.program = this.createProgram(gl, vertexShader, fragmentShader);

        // Get attribute and uniform locations
        const positionLocation = gl.getAttribLocation(cardData.program, 'a_position');
        const texCoordLocation = gl.getAttribLocation(cardData.program, 'a_texCoord');
        
        cardData.uniforms = {
            texture: gl.getUniformLocation(cardData.program, 'u_texture'),
            time: gl.getUniformLocation(cardData.program, 'u_time'),
            distortion: gl.getUniformLocation(cardData.program, 'u_distortion'),
            resolution: gl.getUniformLocation(cardData.program, 'u_resolution'),
            imageSize: gl.getUniformLocation(cardData.program, 'u_imageSize')
        };

        // Create buffers
        const positions = new Float32Array([
            -1, -1,
             1, -1,
            -1,  1,
             1,  1,
        ]);

        const texCoords = new Float32Array([
            0, 1,
            1, 1,
            0, 0,
            1, 0,
        ]);

        const positionBuffer = gl.createBuffer();
        gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
        gl.bufferData(gl.ARRAY_BUFFER, positions, gl.STATIC_DRAW);

        const texCoordBuffer = gl.createBuffer();
        gl.bindBuffer(gl.ARRAY_BUFFER, texCoordBuffer);
        gl.bufferData(gl.ARRAY_BUFFER, texCoords, gl.STATIC_DRAW);

        // Store buffer info
        cardData.positionBuffer = positionBuffer;
        cardData.texCoordBuffer = texCoordBuffer;
        cardData.positionLocation = positionLocation;
        cardData.texCoordLocation = texCoordLocation;

        // Load texture
        this.loadTexture(cardData, imageSrc);
    }

    createShader(gl, type, source) {
        const shader = gl.createShader(type);
        gl.shaderSource(shader, source);
        gl.compileShader(shader);

        if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
            console.error('Shader compilation error:', gl.getShaderInfoLog(shader));
            gl.deleteShader(shader);
            return null;
        }

        return shader;
    }

    createProgram(gl, vertexShader, fragmentShader) {
        const program = gl.createProgram();
        gl.attachShader(program, vertexShader);
        gl.attachShader(program, fragmentShader);
        gl.linkProgram(program);

        if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
            console.error('Program linking error:', gl.getProgramInfoLog(program));
            gl.deleteProgram(program);
            return null;
        }

        return program;
    }

    loadTexture(cardData, imageSrc) {
        const { gl } = cardData;
        const texture = gl.createTexture();

        // Create a temporary 1x1 pixel texture while image loads
        gl.bindTexture(gl.TEXTURE_2D, texture);
        gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, 1, 1, 0, gl.RGBA, gl.UNSIGNED_BYTE, new Uint8Array([255, 255, 255, 255]));

        const image = new Image();
        image.crossOrigin = 'anonymous';

        image.onload = () => {
            gl.bindTexture(gl.TEXTURE_2D, texture);
            gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, image);

            // Store image dimensions for aspect ratio calculations
            cardData.imageWidth = image.width;
            cardData.imageHeight = image.height;

            // Set texture parameters for smooth rendering
            gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
            gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
            gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
            gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);

            // Mark texture as loaded and ensure initial render
            cardData.textureLoaded = true;
            cardData.needsInitialRender = true;

            // Start render loop to ensure initial frame is rendered
            this.startRenderLoop();
        };

        image.src = imageSrc;
        cardData.texture = texture;
        cardData.textureLoaded = false;
    }

    setupEventListeners(cardData) {
        const { card } = cardData;

        card.addEventListener('mouseenter', () => {
            cardData.isHovered = true;
            cardData.distortionTarget = 1.0;
            cardData.transitionStartTime = Date.now();
            cardData.transitionStartValue = cardData.distortionCurrent;
            cardData.isTransitioning = true;
            this.startRenderLoop();
        });

        card.addEventListener('mouseleave', () => {
            cardData.isHovered = false;
            cardData.distortionTarget = 0.0;
            cardData.transitionStartTime = Date.now();
            cardData.transitionStartValue = cardData.distortionCurrent;
            cardData.isTransitioning = true;
            this.startRenderLoop();
        });
    }

    render(currentTime = 0) {
        // Adaptive frame rate limiting based on device performance
        const adaptiveFrameInterval = this.isLowPerformanceDevice ?
            1000 / 30 : // 30 FPS for low-end devices
            this.frameInterval; // 60 FPS for high-end devices

        if (currentTime - this.lastFrameTime < adaptiveFrameInterval) {
            this.frameId = requestAnimationFrame((time) => this.render(time));
            return;
        }
        this.lastFrameTime = currentTime;

        // Only render cards that need updates
        let needsRender = false;
        this.projectCards.forEach(cardData => {
            if (this.shouldRenderCard(cardData)) {
                this.renderCard(cardData);
                needsRender = true;
            }
        });

        // Continue rendering only if needed
        if (needsRender || this.hasActiveTransitions()) {
            this.frameId = requestAnimationFrame((time) => this.render(time));
        } else {
            this.isRendering = false;
        }
    }

    shouldRenderCard(cardData) {
        // Check if card is visible in viewport
        const rect = cardData.card.getBoundingClientRect();
        const isVisible = rect.top < window.innerHeight && rect.bottom > 0;

        return isVisible && (
            cardData.needsInitialRender ||
            cardData.isTransitioning ||
            cardData.distortionCurrent > 0.001 ||
            cardData.isHovered
        );
    }

    hasActiveTransitions() {
        return this.projectCards.some(card =>
            card.needsInitialRender ||
            card.isTransitioning ||
            card.distortionCurrent > 0.001
        );
    }

    startRenderLoop() {
        if (!this.isRendering) {
            this.isRendering = true;
            this.frameId = requestAnimationFrame((time) => this.render(time));
        }
    }

    stopRenderLoop() {
        if (this.frameId) {
            cancelAnimationFrame(this.frameId);
            this.frameId = null;
            this.isRendering = false;
        }
    }

    renderCard(cardData) {
        const { gl, program, texture, uniforms, canvas, startTime } = cardData;

        if (!program || !texture || !cardData.textureLoaded) return;

        // Mark that initial render is no longer needed after first render
        if (cardData.needsInitialRender) {
            cardData.needsInitialRender = false;
            cardData.hasRenderedOnce = true;
        }

        // Clear canvas
        gl.clearColor(0, 0, 0, 0);
        gl.clear(gl.COLOR_BUFFER_BIT);

        // Use program
        gl.useProgram(program);

        // Set up position attribute
        gl.bindBuffer(gl.ARRAY_BUFFER, cardData.positionBuffer);
        gl.enableVertexAttribArray(cardData.positionLocation);
        gl.vertexAttribPointer(cardData.positionLocation, 2, gl.FLOAT, false, 0, 0);

        // Set up texture coordinate attribute
        gl.bindBuffer(gl.ARRAY_BUFFER, cardData.texCoordBuffer);
        gl.enableVertexAttribArray(cardData.texCoordLocation);
        gl.vertexAttribPointer(cardData.texCoordLocation, 2, gl.FLOAT, false, 0, 0);

        // Set uniforms
        gl.activeTexture(gl.TEXTURE0);
        gl.bindTexture(gl.TEXTURE_2D, texture);
        gl.uniform1i(uniforms.texture, 0);

        const currentTime = (Date.now() - startTime) / 1000;
        gl.uniform1f(uniforms.time, currentTime);

        // Enhanced smooth transition with easing
        if (cardData.isTransitioning) {
            const elapsed = Date.now() - cardData.transitionStartTime;
            const progress = Math.min(elapsed / cardData.transitionDuration, 1.0);

            // Use different easing for hover in vs hover out
            const easedProgress = cardData.isHovered ?
                this.easeOutCubic(progress) :
                this.easeInOutCubic(progress);

            const startValue = cardData.transitionStartValue;
            const targetValue = cardData.distortionTarget;

            cardData.distortionCurrent = startValue + (targetValue - startValue) * easedProgress;

            // End transition when complete
            if (progress >= 1.0) {
                cardData.distortionCurrent = cardData.distortionTarget;
                cardData.isTransitioning = false;
            }
        }

        gl.uniform1f(uniforms.distortion, cardData.distortionCurrent);

        gl.uniform2f(uniforms.resolution, canvas.width, canvas.height);

        // Pass image size for proper aspect ratio calculation
        const imageWidth = cardData.imageWidth || canvas.width;
        const imageHeight = cardData.imageHeight || canvas.height;
        gl.uniform2f(uniforms.imageSize, imageWidth, imageHeight);

        // Draw
        gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
    }
}

// Initialize the WebGL distortion system
new WebGLDistortion();
